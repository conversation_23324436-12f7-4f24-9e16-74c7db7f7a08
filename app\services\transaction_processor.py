from sqlalchemy.orm import Session
from models.transaction import Transaction, TransactionType, TransactionStatus
from models.partner import Partner
from schemas.transaction import TransactionCreate
from decimal import Decimal
from typing import Dict, Any
import uuid

class TransactionProcessor:
    @staticmethod
    def create_transaction(db: Session, transaction_data: TransactionCreate, user_id: int) -> Transaction:
        """Create a new transaction with automatic USD conversion"""
        
        # Calculate USD equivalent
        usd_equivalent = transaction_data.amount
        if transaction_data.currency != 'USD' and transaction_data.exchange_rate:
            usd_equivalent = transaction_data.amount / transaction_data.exchange_rate
        
        # Generate reference number
        reference_number = f"TXN-{uuid.uuid4().hex[:8].upper()}"
        
        # Create transaction
        db_transaction = Transaction(
            partner_id=transaction_data.partner_id,
            transaction_type=transaction_data.transaction_type,
            amount=transaction_data.amount,
            currency=transaction_data.currency,
            exchange_rate=transaction_data.exchange_rate,
            usd_equivalent=usd_equivalent,
            reference_number=reference_number,
            status=TransactionStatus.COMPLETED,
            channel=transaction_data.channel,
            notes=transaction_data.notes,
            created_by=user_id
        )
        
        db.add(db_transaction)
        db.commit()
        db.refresh(db_transaction)
        
        return db_transaction
    
    @staticmethod
    def process_mixed_return(
        db: Session, 
        partner_id: int, 
        usd_amount: Decimal, 
        ngn_amount: Decimal, 
        current_rate: Decimal,
        user_id: int
    ) -> Dict[str, Any]:
        """Process mixed currency returns (USD + NGN)"""
        
        total_usd_returned = usd_amount
        if ngn_amount and current_rate:
            total_usd_returned += ngn_amount / current_rate
        
        # Create return transaction
        transaction_data = TransactionCreate(
            partner_id=partner_id,
            transaction_type=TransactionType.FUNDS_RETURNED,
            amount=total_usd_returned,
            currency="USD",
            notes=f"Mixed return: ${usd_amount} + ₦{ngn_amount} @ {current_rate}"
        )
        
        transaction = TransactionProcessor.create_transaction(db, transaction_data, user_id)
        
        # Calculate balance allocation
        from .balance_calculator import BalanceCalculator
        balance_details = BalanceCalculator.calculate_chronological_balance(
            db, partner_id, total_usd_returned
        )
        
        return {
            "transaction": transaction,
            "balance_allocation": balance_details,
            "total_usd_returned": float(total_usd_returned)
        }
