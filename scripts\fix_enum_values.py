#!/usr/bin/env python3
"""
Fix enum values in the database to match model expectations
"""

import sqlite3
import os
import shutil
from datetime import datetime

def fix_enum_values():
    """Fix enum values in the database"""
    print("🔧 Fixing enum values in database...")
    
    db_path = os.path.join(os.path.dirname(__file__), '..', 'bureau_de_change.db')
    backup_path = f"{db_path}.enum_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    # Create backup
    print(f"📦 Creating backup: {backup_path}")
    shutil.copy2(db_path, backup_path)
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        print("🔄 Updating transaction_type values...")
        
        # Fix transaction_type values
        cursor.execute("UPDATE transactions SET transaction_type = 'funds_given' WHERE transaction_type = 'FUNDS_GIVEN'")
        cursor.execute("UPDATE transactions SET transaction_type = 'funds_returned' WHERE transaction_type = 'FUNDS_RETURNED'")
        cursor.execute("UPDATE transactions SET transaction_type = 'partial_return' WHERE transaction_type = 'PARTIAL_RETURN'")
        
        print("🔄 Updating status values...")
        
        # Fix status values
        cursor.execute("UPDATE transactions SET status = 'pending' WHERE status = 'PENDING'")
        cursor.execute("UPDATE transactions SET status = 'completed' WHERE status = 'COMPLETED'")
        cursor.execute("UPDATE transactions SET status = 'cancelled' WHERE status = 'CANCELLED'")
        
        # Commit changes
        conn.commit()
        
        # Verify the fix
        print("\n✅ Verification:")
        
        cursor.execute("SELECT DISTINCT transaction_type FROM transactions")
        types = [t[0] for t in cursor.fetchall()]
        print(f"   Transaction types: {types}")
        
        cursor.execute("SELECT DISTINCT status FROM transactions")
        statuses = [s[0] for s in cursor.fetchall()]
        print(f"   Statuses: {statuses}")
        
        cursor.execute("SELECT DISTINCT channel FROM transactions")
        channels = [c[0] for c in cursor.fetchall()]
        print(f"   Channels: {channels}")
        
        # Show sample updated transaction
        cursor.execute("SELECT id, transaction_type, status, channel FROM transactions LIMIT 1")
        transaction = cursor.fetchone()
        if transaction:
            print(f"\n📊 Sample transaction:")
            print(f"   ID: {transaction[0]}, Type: {transaction[1]}, Status: {transaction[2]}, Channel: {transaction[3]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        # Restore backup on error
        print("🔄 Restoring backup...")
        shutil.copy2(backup_path, db_path)
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    if fix_enum_values():
        print("\n🎉 Enum values fixed successfully!")
        print("💡 The server should now work properly.")
        print("🔄 Restart the server to ensure it picks up the changes.")
    else:
        print("\n❌ Failed to fix enum values.")
        print("🔧 Check the error messages above for details.")
