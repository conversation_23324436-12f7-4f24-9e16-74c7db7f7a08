from pydantic import BaseModel, validator
from typing import Optional
from datetime import datetime
from decimal import Decimal
from models.transaction import TransactionType, TransactionStatus, TransactionChannel
from .partner import Partner

class TransactionBase(BaseModel):
    partner_id: int
    transaction_type: TransactionType
    amount: Decimal
    currency: str
    exchange_rate: Optional[Decimal] = None
    channel: TransactionChannel = TransactionChannel.CASH
    notes: Optional[str] = None

class TransactionCreate(TransactionBase):
    @validator('currency')
    def validate_currency(cls, v):
        allowed_currencies = ['NGN', 'USD', 'GBP', 'EUR']
        if v not in allowed_currencies:
            raise ValueError(f'Currency must be one of {allowed_currencies}')
        return v
    
    @validator('amount')
    def validate_amount(cls, v):
        if v <= 0:
            raise ValueError('Amount must be greater than 0')
        return v

    @validator('channel')
    def validate_channel(cls, v):
        allowed_channels = ['cash', 'bank_transfer']
        if v not in allowed_channels:
            raise ValueError(f'Channel must be one of {allowed_channels}')
        return v

class TransactionUpdate(BaseModel):
    status: Optional[TransactionStatus] = None
    notes: Optional[str] = None

class Transaction(TransactionBase):
    id: int
    usd_equivalent: Decimal
    reference_number: Optional[str]
    status: TransactionStatus
    channel: TransactionChannel
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class TransactionWithPartner(Transaction):
    partner: Partner
