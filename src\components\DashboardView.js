import React from 'react';
import { DollarSign, TrendingUp, Users, Calendar } from 'lucide-react';

const DashboardView = ({ stats, transactions, partners }) => {
  const formatCurrency = (amount, currency = 'USD') => {
    if (currency === 'USD') {
      return `$${amount.toLocaleString()}`;
    } else if (currency === 'NGN') {
      return `₦${amount.toLocaleString()}`;
    } else if (currency === 'EUR') {
      return `€${amount.toLocaleString()}`;
    } else if (currency === 'GBP') {
      return `£${amount.toLocaleString()}`;
    }
    return `${amount.toLocaleString()} ${currency}`;
  };

  const recentTransactions = transactions.slice(0, 5);

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <DollarSign className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Outstanding Balances</p>
              <div className="space-y-1">
                {/* NGN - Priority (Bold) */}
                {stats.outstanding_balances?.NGN?.amount > 0 && (
                  <p className="text-lg font-bold text-gray-900">
                    {formatCurrency(stats.outstanding_balances.NGN.amount, 'NGN')}
                  </p>
                )}

                {/* USD */}
                <p className="text-lg font-semibold text-gray-800">
                  {formatCurrency(stats.total_outstanding || 0, 'USD')}
                </p>

                {/* EUR */}
                {stats.outstanding_balances?.EUR?.amount > 0 && (
                  <p className="text-sm text-gray-600">
                    {formatCurrency(stats.outstanding_balances.EUR.amount, 'EUR')}
                  </p>
                )}

                {/* GBP */}
                {stats.outstanding_balances?.GBP?.amount > 0 && (
                  <p className="text-sm text-gray-600">
                    {formatCurrency(stats.outstanding_balances.GBP.amount, 'GBP')}
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <Users className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Partners</p>
              <p className="text-2xl font-bold text-gray-900">
                {stats.active_partners || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <TrendingUp className="h-6 w-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Today's Transactions</p>
              <p className="text-2xl font-bold text-gray-900">
                {stats.today_transactions || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Calendar className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Pending Partners</p>
              <p className="text-2xl font-bold text-gray-900">
                {stats.pending_partners || 0}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Transactions */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Recent Transactions</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Partner
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  USD Equivalent
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {recentTransactions.map((transaction) => {
                const partner = partners.find(p => p.id === transaction.partner_id);
                return (
                  <tr key={transaction.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {partner?.name || 'Unknown Partner'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        transaction.transaction_type === 'FUNDS_GIVEN' 
                          ? 'bg-red-100 text-red-800' 
                          : 'bg-green-100 text-green-800'
                      }`}>
                        {transaction.transaction_type === 'FUNDS_GIVEN' ? 'Given' : 'Returned'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatCurrency(transaction.amount, transaction.currency)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatCurrency(transaction.usd_equivalent)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(transaction.created_at).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        transaction.status === 'COMPLETED' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {transaction.status}
                      </span>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
        {recentTransactions.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No transactions found
          </div>
        )}
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Financial Summary</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">Total Given (USD):</span>
              <span className="font-medium">{formatCurrency(stats.total_given || 0)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Total Returned (USD):</span>
              <span className="font-medium">{formatCurrency(stats.total_returned || 0)}</span>
            </div>
            <div className="border-t pt-3 space-y-2">
              <div className="text-gray-900 font-medium mb-2">Outstanding Balances:</div>

              {/* NGN - Priority */}
              {stats.outstanding_balances?.NGN?.amount > 0 && (
                <div className="flex justify-between">
                  <span className="text-gray-700 font-semibold">Naira (NGN):</span>
                  <span className="font-bold text-lg text-green-700">
                    {formatCurrency(stats.outstanding_balances.NGN.amount, 'NGN')}
                  </span>
                </div>
              )}

              {/* USD */}
              <div className="flex justify-between">
                <span className="text-gray-700">US Dollar (USD):</span>
                <span className="font-semibold text-blue-700">
                  {formatCurrency(stats.total_outstanding || 0, 'USD')}
                </span>
              </div>

              {/* EUR */}
              {stats.outstanding_balances?.EUR?.amount > 0 && (
                <div className="flex justify-between">
                  <span className="text-gray-700">Euro (EUR):</span>
                  <span className="font-medium text-purple-700">
                    {formatCurrency(stats.outstanding_balances.EUR.amount, 'EUR')}
                  </span>
                </div>
              )}

              {/* GBP */}
              {stats.outstanding_balances?.GBP?.amount > 0 && (
                <div className="flex justify-between">
                  <span className="text-gray-700">British Pound (GBP):</span>
                  <span className="font-medium text-indigo-700">
                    {formatCurrency(stats.outstanding_balances.GBP.amount, 'GBP')}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
          <div className="space-y-3">
            <button className="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50">
              <div className="font-medium text-gray-900">View All Partners</div>
              <div className="text-sm text-gray-600">Manage trading partners</div>
            </button>
            <button className="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50">
              <div className="font-medium text-gray-900">Daily Report</div>
              <div className="text-sm text-gray-600">Generate today's report</div>
            </button>
            <button className="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50">
              <div className="font-medium text-gray-900">Balance Processing</div>
              <div className="text-sm text-gray-600">Process returns and balances</div>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardView;
